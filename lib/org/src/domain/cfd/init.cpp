   using namespace std;

#  include <sstream>
#  include <domain/cfd/domain.h>


   void cFdDomain::init()
  {
      Int    ig;
      Int   *idone;
      bool   bsu;

      idone = new Int [nv];
      for(Int iv=0; iv<nv; iv++) idone[iv] = -1;
      cfl= cfl0;

//initialize volume
//      init_vol();


//initialize frame speed
//      init_frame();

//initialize boundary faces
//      init_bcs();
      json_config_sol(idone);


      if(tm>0)
     {
         //unsteady simulation restart
         //for unsteady simulation, you can not restart from one turbulence model to another
         //force to use all the variables from the restart file
         for(Int iv=0; iv<nv; iv++) idone[iv] = 1;
     }


     #pragma acc enter data copyin(idone[0:nv])

      fld->nondim( 0,nq, q, idone ); //g
      omega/= fld->units(0);
      for( ig=0;ig<ng;ig++ )
     {
         fld->nondim( 0,nbb[ig], qb0[ig], idone );  //g
         omegb[ig]/= fld->units(0); //c

         bbj[ig]->setbndtag(bgnm[ig]);
     }
      fld->nondim_time(&dtm);

      fld->auxv(0, nq, q, aux,"d");
      if( unst )
     {
         if(!bsu)
        {
            //unsteady and not restarted from a previous unsteady solution
            for(Int il=0; il<ntlv; il++)
           {
               fld->cnsv( 0,nq, q, aux, u[il], "d" );
           }
        }
     }

     #pragma acc update device(omegb[0:ng])

      movegrid();
      frame();
      weights();

     #pragma acc exit data delete(idone[0:nv])
      delete[] idone; idone=NULL;
  }

   void cFdDomain::init_z()
  {
      string cpath, devnm, fnm;
      ifstream fle;
      Int i, j, ifre, ig0, tmpnfre, ig;
      string tmpnm[MXNFRE], tmpstr;
      Real tmpfreq[MXNFRE];

      readzsetup();

      for(ifre=0; ifre<nfre; ifre++)
     {
         cfl_z[ifre] = cfl0;
         freq0[ifre] /= 100;
     }

      if(nx==3)
     {
         for(ig=0; ig<ng; ig++)
        {
            readzbnd( ig );
        }
     }

      //restart the harmonic solutions
      //read_z();

      //cout << "nfre " << nfre << " ============ \n";
  }

   void cFdDomain::readzbnd( Int ig0 )
  {
//      string fnm, cpath, devnm, sdum;
//      Int nlev, tmpnfre, ifre, nb, iv, il, ib, jl, nbld;;
//      Real *tmpq_re[MXNFRE][10], *tmpq_im[MXNFRE][10];
//      Real y[3], rt[2], *levx[2], rmax, rmin, w, pitch, dt;
//      istringstream ss;
//      complex<Real> dz, img(0,1), z;
//      Real tmp_re, tmp_im;
//      Int nv0 = 5;
//
//      nb = nbb[ig0];
//
//      cpath= dev->getcpath();
//      devnm = dev->getname();
//
//      fnm= cpath+ "/" + devnm + "." + bgnm[ig0] + ".zbc";
//
//      cout << " ======================================== readzbnd\n";
//
//      assert(nx==3);
//      ifstream fle;
//      fle.open(fnm.c_str());
//      if(!fle.good())
//     {
//         cout << "can not read file " << fnm << " Harmonics are not specified for boundary " << bgnm[ig0] << " of " << devnm << "\n";
//         return;
//     }
//      cout << "read harmonic boundary conditions for boundary " << ig0 << " " << bgnm[ig0] << "\n";
//      getline(fle, sdum);
//      ss.clear();
//      ss.str(sdum);
//      ss >> sdum >> nbld >> nlev >> tmpnfre;
//      pitch = pi2/nbld;
//      cout << "number of levels in zbc " << nlev << "\n";
//      for(ifre=0; ifre<tmpnfre; ifre++)
//     {
//         for(iv=0; iv<nv0; iv++)
//        {
//            tmpq_re[ifre][iv] = new Real [nlev];
//            tmpq_im[ifre][iv] = new Real [nlev];
//        }
//     }
//      levx[0] = new Real [nlev]; //r
//      levx[1] = new Real [nlev]; //t
//      rmin = big;
//      rmax =-big;
//
//      for(il=0; il<nlev; il++)
//     {
//         for(ifre=0; ifre<tmpnfre; ifre++)
//        {
//            getline(fle, sdum);
//            ss.clear();
//            ss.str(sdum);
//            ss >> levx[0][il] >> levx[1][il];
//            for(iv=0; iv<nv0; iv++)
//           {
//               ss >> tmpq_re[ifre][iv][il];
//           }
//            for(iv=0; iv<nv0; iv++)
//           {
//               ss >> tmpq_im[ifre][iv][il];
//           }
//        }
//     }
//      fle.close();
//
//      for(il=0; il<nlev; il++)
//     {
//         for(ifre=0; ifre<tmpnfre; ifre++)
//        {
//            for(iv=0; iv<nv0; iv++)
//           {
//               cout << tmpq_re[ifre][iv][il] << " ";
//           }
//            for(iv=0; iv<nv0; iv++)
//           {
//               cout << tmpq_im[ifre][iv][il] << " ";
//           }
//            cout << "\n";
//        }
//     }
//
//      rmax = levx[0][nlev-1];
//      rmin = levx[0][0];
//      for(ib=0; ib<nb; ib++)
//     {
//         y[0] = xb[ig0][0][ib];
//         y[1] = xb[ig0][1][ib];
//         y[2] = xb[ig0][2][ib];
//
//            //t = atan2(xb[1][ib], xb[2][ib]);
//            //r = sqrt(xb[1][ib]*xb[1][ib] + xb[2][ib]*xb[2][ib]);
//
//         rt[0] = sqrt(y[1]*y[1] + y[2]*y[2]);
//         rt[1] = atan2(y[1], y[2]);
//
//         for(il=0; il<nlev-1; il++)
//        {
//            if(rt[0]<rmin)
//           {
//               jl=0;
//               for(ifre=0; ifre<tmpnfre; ifre++)
//              {
//                  for(iv=0; iv<nv0; iv++)
//                 {
//                    //qb[iv][ib] = tmpq[iv][jl];
//                    //zb0_re[ifre][ig0][iv][ib] = tmpq_re[ifre][iv][jl];
//                    //zb0_im[ifre][ig0][iv][ib] = tmpq_im[ifre][iv][jl];
//                    tmp_re = tmpq_re[ifre][iv][jl];
//                    tmp_im = tmpq_im[ifre][iv][jl];
//
//                    dt = rt[1] - levx[1][jl];
//
//                    z = tmp_re + tmp_im * img;
//                    dz = cos(pi2*(ifre+1)*dt/pitch) + sin(pi2*(ifre+1)*dt/pitch)*img;
//                    //dz = cos(pi2*ibpa[ifre]*dt/pitch) + sin(pi2*ibpa[ifre]*dt/pitch)*img;
//                    z = z*dz;
//
//                    zb0_re[ifre][ig0][iv][ib] = z.real();
//                    zb0_im[ifre][ig0][iv][ib] = z.imag();
//                 }
//              }
//               break;
//           }
//            else if(rt[0]>rmax)
//           {
//               jl=nlev-1;
//               for(ifre=0; ifre<tmpnfre; ifre++)
//              {
//                  for(iv=0; iv<nv0; iv++)
//                 {
//                    //qb[iv][ib] = tmpq[iv][jl];
//                    //zb0_re[ifre][ig0][iv][ib] = tmpq_re[ifre][iv][jl];
//                    //zb0_im[ifre][ig0][iv][ib] = tmpq_im[ifre][iv][jl];
//                    tmp_re = tmpq_re[ifre][iv][jl];
//                    tmp_im = tmpq_im[ifre][iv][jl];
//
//                    dt = rt[1] - levx[1][jl];
//
//                    z = tmp_re + tmp_im * img;
//                    dz = cos(pi2*(ifre+1)*dt/pitch) + sin(pi2*(ifre+1)*dt/pitch)*img;
//                    //dz = cos(pi2*ibpa[ifre]*dt/pitch) + sin(pi2*ibpa[ifre]*dt/pitch)*img;
//                    z = z*dz;
//
//                    zb0_re[ifre][ig0][iv][ib] = z.real();
//                    zb0_im[ifre][ig0][iv][ib] = z.imag();
//                 }
//              }
//               break;
//           }
//            else if(rt[0]>=levx[0][il] && rt[0]<=levx[0][il+1])
//           {
//               w = rt[0] - levx[0][il];
//               w/= levx[0][il+1] - levx[0][il];
//               for(ifre=0; ifre<tmpnfre; ifre++)
//              {
//                  for(iv=0; iv<nv0; iv++)
//                 {
//                    //qb[iv][ib] = tmpq[iv][il]*(1-w) + tmpq[iv][il+1]*w;
//                    //zb0_re[ifre][ig0][iv][ib] = tmpq_re[ifre][iv][il]*(1-w) + tmpq_re[ifre][iv][il+1]*w;
//                    //zb0_im[ifre][ig0][iv][ib] = tmpq_im[ifre][iv][il]*(1-w) + tmpq_im[ifre][iv][il+1]*w;
//                    tmp_re = tmpq_re[ifre][iv][il]*(1-w) + tmpq_re[ifre][iv][il+1]*w;
//                    tmp_im = tmpq_im[ifre][iv][il]*(1-w) + tmpq_im[ifre][iv][il+1]*w;
//
//                    dt = rt[1] - (levx[1][il]*(1-w) + levx[1][il+1]*w);
//
//                    z = tmp_re + tmp_im * img;
//                    dz = cos(pi2*(ifre+1)*dt/pitch) + sin(pi2*(ifre+1)*dt/pitch)*img;
//                    //dz = cos(pi2*ibpa[ifre]*dt/pitch) + sin(pi2*ibpa[ifre]*dt/pitch)*img;
//                    z = z*dz;
//
//                    zb0_re[ifre][ig0][iv][ib] = z.real();
//                    zb0_im[ifre][ig0][iv][ib] = z.imag();
//                 }
//              }
//               break;
//           }
//        }
//     }
//
//      for(ifre=0; ifre<tmpnfre; ifre++)
//     {
//         coo->zvel( 0,nb, NULL, xb[ig0], zb0_re[ifre][ig0], zb0_re[ifre][ig0] );
//         coo->zvel( 0,nb, NULL, xb[ig0], zb0_im[ifre][ig0], zb0_im[ifre][ig0] );
//     }
//
////      for(ifre=0; ifre<tmpnfre; ifre++)
////     {
////         for(ib=0; ib<nb; ib++)
////        {
////            y[0] = xb[ig0][0][ib];
////            y[1] = xb[ig0][1][ib];
////            y[2] = xb[ig0][2][ib];
////   
////            //t = atan2(xb[1][ib], xb[2][ib]);
////            //r = sqrt(xb[1][ib]*xb[1][ib] + xb[2][ib]*xb[2][ib]);
////
////            rt[0] = sqrt(y[1]*y[1] + y[2]*y[2]);
////            rt[1] = atan2(y[1], y[2]);
////            cout << rt[1] << " ";      
////            for(iv=0; iv<nv0; iv++)
////           {
////               cout << zb0_re[ifre][ig0][iv][ib] << " ";    
////           }
////            cout << "\n";
////        }
////     }
//
//      for(ifre=0; ifre<tmpnfre; ifre++)
//     {
//         for(iv=0; iv<nv0; iv++)
//        {
//            delete[] tmpq_re[ifre][iv]; tmpq_re[ifre][iv]=NULL;
//            delete[] tmpq_im[ifre][iv]; tmpq_im[ifre][iv]=NULL;
//        }
//     }
//      delete[] levx[0]; levx[0]=NULL;
//      delete[] levx[1]; levx[1]=NULL;
//
//      //exit(0);
  }

   /*void cFdDomain::readzsetup()
  {
      Int i, j, tmpnfre, ig0, ig;
      string cpath, devnm, fnm, tmpnm[MXNFRE], tmpstr;
      ifstream fle;

      cpath= dev->getcpath();
      devnm = dev->getname();

      fnm= cpath+ "/" + devnm + ".zsetup";
      fle.open(fnm.c_str());
      fle >> tmpnfre;
      if(tmpnfre!=nfre)
     {
         cout << "Error: the number of harmonics does not match in the setup file " << tmpnfre << " " << nfre << "\n";
         assert(0);
     }

      j=0;
      for(i=0; i<nfre; i++)
     {
         fle >> freq0[j] >> ibpa[j] >> tmpnm[j]; j++;
     }
      fle.close();

      tmpstr = tmpnm[0];
      j=0;
      for(i=0; i<nfre; i++)
     {
         ig0 = -1;
         for(ig=0; ig<ng; ig++)
        {
            if(tmpnm[i]==bgnm[ig])
           {
               ig0 = ig;
               break;
           }
        }
         if(ig0<0)
        {
            cout << "Error: the boundary tag " << tmpstr << " for the zsetup does not exists\n";
        }
         assert(ig0>=0);

         if(tmpstr!=bgnm[ig0])
        {
           tmpstr = bgnm[ig0];
           j=0;
        }

         bharm[i][0] = ig0;
         bharm[i][1] = j;
         j++;

         bbj[ig0]->setnfre_reci(j);
     }

      for(i=0; i<nfre; i++)
     {
         cout << bgnm[bharm[i][0]] << " " << bharm[i][1] << "\n";
     }
  }*/

   void cFdDomain::readzsetup()
  {
//      Int i, tmpng, ig, jg, kg, tmpnfre, tmpreci[MxNBG], tmpsend[MxNBG], ifre, jfre;
//      string cpath, devnm, fnm, tmpnm[MxNBG];
//      ifstream fle;
//      Real tmpomega[MxNBG], tmpibpa[MxNBG];
//      Real tmpomegalin[MxNBG][MXNFRE];
//      istringstream ss;
//      string sdum;
//      Int tmpsendlinn[MXNFRE];
//      Int tmpdftlin[MXNFRE][MXNFRE], tmpsendlinwavnum[MXNFRE][MXNFRE];
//      Int hlp[200][2];
//
//      cpath= dev->getcpath();
//      devnm = dev->getname();
//
//      for(ig=0; ig<MxNBG; ig++)
//     {
//         tmpomega[ig] = 0;
//         tmpibpa[ig] = 0;
//         tmpreci[ig] = 0;
//         tmpsend[ig] = 0;
//     }
//
//      fnm= cpath+ "/" + devnm + ".zsetup";
//      fle.open(fnm.c_str());
//      if(!fle.good()) cout << "Error: can not open zsetup file " << fnm << "\n";
//    
////receive 
//      getline(fle, sdum);
//
//      getline(fle, sdum);
//      ss.clear();
//      ss.str(sdum);
//      ss >> tmpng; 
//
//      nfre=0;
//      for(ig=0; ig<tmpng; ig++)
//     {
//         getline(fle, sdum);
//         ss.clear();
//         ss.str(sdum);
//         ss >> tmpnm[ig] >> tmpomega[ig] >> tmpibpa[ig];
//
//         cout << tmpnm[ig] << " " <<  tmpomega[ig] << " " <<  tmpibpa[ig] << "\n";
//
//         for(jg=0; jg<ng; jg++)
//        {
//            if(tmpnm[ig]==bgnm[jg])
//           {
//               freq0[nfre] = tmpomega[ig];
//               ibpa[nfre] = tmpibpa[ig];
//
//               bharm[nfre][0] = jg;
//               bharm[nfre][1] = tmpreci[jg];
//
//               nfre++;
//               tmpreci[jg]++;
//
//               break;
//           }
//        }
//     }
//
//      for(ig=0; ig<ng; ig++) bbj[ig]->setnfre_reci(tmpreci[ig]);
//
//      for(i=0; i<nfre; i++)
//     {
//         cout << i << " " << bgnm[bharm[i][0]] << " " << bharm[i][1] << " " << freq0[i] << " " << ibpa[i] << " ------------ \n";
//     }
//
////send-mean
//      getline(fle, sdum);
//
//      getline(fle, sdum);
//      ss.clear();
//      ss.str(sdum);
//      ss >> tmpng;
//      for(ig=0; ig<tmpng; ig++)
//     {
//         getline(fle, sdum);
//         ss.clear();
//         ss.str(sdum);
//         ss >> tmpnm[ig] >> tmpsend[ig];
//
//         for(jg=0; jg<ng; jg++)
//        {
//            if(tmpnm[ig]==bgnm[jg])
//           {
//               bbj[jg]->setnfre_send(tmpsend[ig]);
//               break;
//           }
//        }
//     }
//      
////send-lin
//      getline(fle, sdum);
//
//      tmpng = 0;
//      getline(fle, sdum);
//      ss.clear();
//      ss.str(sdum);
//      ss >> tmpng; 
//
//      for(ig=0; ig<tmpng; ig++)
//     {
//         getline(fle, sdum);
//         ss.clear();
//         ss.str(sdum);
//         ss >> tmpnm[ig] >> tmpsendlinn[ig];
//
//         for(ifre=0; ifre<tmpsendlinn[ig]; ifre++)
//        {
//            getline(fle, sdum);
//            ss.clear();
//            ss.str(sdum);
//            ss >> tmpdftlin[ig][ifre];
//            ss >> tmpsendlinwavnum[ig][ifre]; 
//        }
//     }
//
//      for(ig=0; ig<tmpng; ig++) 
//     {
//         for(jg=0; jg<ng; jg++)
//        {
//            if(tmpnm[ig]==bgnm[jg])
//           {
//               bbj[jg]->set_nfre_send_lin(tmpsendlinn[ig]); 
//               bbj[jg]->set_lin_dft(tmpdftlin[ig]);
//               bbj[jg]->set_send_lin_wavnum( tmpsendlinwavnum[ig] );
//               break;
//           }
//        }
//     }
//      fle.close();
//
//      for(ig=0; ig<ng; ig++)
//     {
//         cout << "boundary " << bgnm[ig] << "\n";
//         bbj[ig]->report_zsetup();
//     }
  }

