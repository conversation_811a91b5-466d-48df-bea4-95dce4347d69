   using namespace std;

#  include <sstream>
#  include <iniParser.h>
#  include <domain/cfd/domain.h>
#  include <domain/cfd/init_sol/read_json.h>


void cFdDomain::json_config_sol( Int *idone )
{
    Int iv, iq, iek, ie, nv0, id, ies, iee;
    string cpath, fnm, devnm, prof_nm;
    Real tmpq[100], u[3];
    bool b_restart;

    cpath= dev->getcpath();
    devnm=dev->getname();
    fnm= cpath+ "/"+devnm+"_init.json";

    // 1. Load file into a string
    ifstream ifs( fnm.c_str() );
    if ( ! ifs )
    {
        cerr << "Error: cannot open '" << fnm << "'\n";
        return;
    }

    ostringstream oss;
    oss << ifs.rdbuf();
    string text = oss.str();

    try
    {
        // 2. Parse root
        cJsonParser parser( text );
        cJsonValue  root   = parser.Parse();
        const map<string,cJsonValue> & rootObj = root.AsObject();

        //init volume
        b_restart = false;
        b_restart = json_config_vol(rootObj);
        if(b_restart) 
       {
           for(iv=0; iv<nv; iv++) idone[iv] = 1;
       }

        //init boundary
        json_config_bnd(rootObj, b_restart);

        //set the medium for monolithic cht and the velocity to zero for solid
        if(fld->gettype()==mfroe_gas_cht)
       {
           nv0 = fld->getnv0();
           for( id=0;id<dev->getncpu()+1;id++ )
          {
              for( iek=0;iek<nek;iek++ )
             {
                 eld[iek]->range( id, &ies,&iee );

                 Int *marker_ptr = smedium_marker[iek];
                 Int *sieq_ptr = sieq[iek];

                 if(iee>ies)
                {
                    #pragma acc enter data copyin (marker_ptr[0:ne[iek]])
                    #pragma acc enter data copyin (sieq_ptr[0:ne[iek]])

                    #pragma acc parallel loop \
                     private(u)\
                     present(sq[0:nv*nq],aux[0:naux*nq],marker_ptr[0:ne[iek]],sieq_ptr[0:ne[iek]],this) \
                     default(none)
                     for(ie=ies; ie<iee; ie++)
                    {
                        //iq = sieq[iek][ie];
                        iq = sieq_ptr[ie];
                        u[0] = sq[ADDR(0,iq,nq)];
                        u[1] = sq[ADDR(1,iq,nq)];
                        u[2] = sq[ADDR(2,iq,nq)];
                        sq[ADDR(0,iq,nq)]     = u[0]*marker_ptr[ie]; //for solid, the marker will be 0
                        sq[ADDR(1,iq,nq)]     = u[1]*marker_ptr[ie];
                        sq[ADDR(2,iq,nq)]     = u[2]*marker_ptr[ie];
                        sq[ADDR(nv0-1,iq,nq)] =      marker_ptr[ie];
                    }

                    #pragma acc exit data delete (marker_ptr[0:ne[iek]])
                    #pragma acc exit data delete (sieq_ptr[0:ne[iek]])
                }
             }
          }
       }
    }
    catch ( const exception & e )
    {
        cerr << "JSON error: " << e.what() << "\n";
    }
}

bool cFdDomain::json_config_vol(const map<string,cJsonValue> & rootObj)
{
    Int iv, nv0;
    Real tmpq[100];
    bool b_restart;

    b_restart = false;

    for(iv=0; iv<nv; iv++) tmpq[iv] = 0;

    nv0 = fld->getnv0();
    // ----- Volume Initialization -----
    cout << "--- Volume Initialization ---\n";
    if ( rootObj.count( "volume_init" ) )
    {
        const map<string,cJsonValue> & volObj = rootObj.at( "volume_init" ).AsObject();

        // New option: profile_file
        if ( volObj.count( "profile_file" ) )
        {
            string volFile = volObj.at( "profile_file" ).AsString();
            cout << "Volume profile file: " << volFile << "\n";
            q263_init(volFile);
        }
        else if ( volObj.count( "uniform" ) )
        {
            const map<string,cJsonValue> & uniObj = volObj.at( "uniform" ).AsObject();

            Real velocity_x  = uniObj.at( "velocity_x"  ).AsNumber();
            Real velocity_y  = uniObj.at( "velocity_y"  ).AsNumber();
            Real velocity_z  = uniObj.at( "velocity_z"  ).AsNumber();
            Real pressure    = uniObj.at( "pressure"    ).AsNumber();
            Real temperature = uniObj.at( "temperature" ).AsNumber();

            cout << fixed << setprecision( 6 );
            cout << setw( 20 ) << "velocity_x:"  << setw(14) << velocity_x  << "\n"
                 << setw( 20 ) << "velocity_y:"  << setw(14) << velocity_y  << "\n"
                 << setw( 20 ) << "velocity_z:"  << setw(14) << velocity_z  << "\n"
                 << setw( 20 ) << "pressure:"    << setw(14) << pressure    << "\n"
                 << setw( 20 ) << "temperature:" << setw(14) << temperature << "\n";

            for(iv=0; iv<nv; iv++) tmpq[iv] = 0;
            tmpq[0] = velocity_x;
            tmpq[1] = velocity_y;
            tmpq[2] = velocity_z;
            tmpq[3] = temperature;
            tmpq[4] = pressure;
            if(vsc->gettype()==komegalowre_visc)
            {
                Real k           = uniObj.at( "k"           ).AsNumber();
                Real omega       = uniObj.at( "omega"       ).AsNumber();

                cout << setw( 20 ) << "k:"           << setw(14) << k           << "\n"
                     << setw( 20 ) << "omega:"       << setw(14) << omega       << "\n";
                tmpq[nv0] = k;
                tmpq[nv0+1] = omega;
            }
            if(fld->gettype()==mfreacting_gas)
            {
                Real z           = uniObj.at( "mixture_fraction" ).AsNumber();
                cout << setw( 20 ) << "mixture_fraction:" << setw(14) << z << "\n";
                tmpq[nv0-1] = z;
            }

            uni_init(tmpq); 
        }
        else if ( volObj.count( "restart" ) )
        {
            restart_vol_init();
            b_restart = true;
        }
        else if ( volObj.count( "single_passage_sol" ) )
        {
            string volFile = volObj.at( "single_passage_sol" ).AsString();
            if( volObj.count( "number_of_passages" ) )
            {
                Int  npsg = volObj.at( "number_of_passages" ).AsNumber();
                cout << "  the single passage soluition file is: " << volFile << "\n";
                cout << "  number of passages to init:           " << npsg << "\n";
                spsg_init(volFile, npsg);
            }
            else
            {
                cout <<"Error: need to specify the number of passages for single_passage_sol\n";
                assert(0);
            }
        }
        else
        {
            cout << "Error: unkown way to initialize the flow field, abort the program\n";
            assert(0);
        }

        if(fld->gettype()==mfroe_gas_cht)
        {
            const map<string,cJsonValue> & solidObj = volObj.at( "solid-prop" ).AsObject();

            Real solid_density = solidObj.at( "density" ).AsNumber();
            Real solid_cp = solidObj.at( "specific-heat" ).AsNumber();
            Real solid_kappa = solidObj.at( "heat-conduction-coff" ).AsNumber();
            cout << solid_density << " " << solid_cp << " " << solid_kappa << "\n";
            fld->set_solid_prop(solid_density, solid_cp, solid_kappa);
        }
    }
    cout << "\n";
    return b_restart;
}

//when I restart boundary bc, only the wall bounary and sliding plane is restarted.
//for freestream, they still read in the bcs
void cFdDomain::json_config_bnd(const map<string,cJsonValue> & rootObj, bool b_restart)
{
    Int iv, ig, nv0;
    string cpath, fnm, devnm, prof_nm;
    Real tmpq[100];
    Int ihlp[100];

    IniParser parser;
    string item, tmpval, str_sec;

    nv0 = fld->getnv0();

  
    parser.parseFromFile("input.au3x");

    //pre-set all boundaries to the frame speed of the volume, they will be modified
    //if specified
    str_sec = dev->getname();
    item = "frame-speed";
    tmpval = parser.getValue(str_sec, item);
    omega = stod(tmpval);
    for( ig=0;ig<ng;ig++ ) omegb[ig] = omega;

    // ----- Boundaries -----
    if ( ! rootObj.count( "boundaries" ) )
    {
        cerr << "Error: no \"boundaries\" section\n";
        return;
    }

    const vector<cJsonValue> & bnds = rootObj.at( "boundaries" ).AsArray();

    cout << "--- Boundaries (" << bnds.size() << ") ---\n";
    if(bnds.size()!=ng)
    {
        cout << "Error: not all boundaries are listed in the json file\n";
        exit(0);
    }

    //if the order of the boundaries are different from the .flag file
    //need a permutation array
    for ( size_t i = 0; i < bnds.size(); ++i )
    {
        ihlp[i] = -1;
        const map<string,cJsonValue> & bndObj = bnds[i].AsObject();
        string name = bndObj.at( "name" ).AsString();
        for(ig=0; ig<ng; ig++)
        {
            if(name==bgnm[ig])
            {
                ihlp[i] = ig;
                break;
            }
        }
         assert(ihlp[i]>=0);
    }

    for ( size_t i = 0; i < bnds.size(); ++i )
    {
        ig = ihlp[i];

        for(iv=0; iv<nv; iv++) tmpq[iv] = 0;

        const map<string,cJsonValue> & bndObj = bnds[i].AsObject();

        string name = bndObj.at( "name" ).AsString();
        string type = bndObj.at( "type" ).AsString();

        cout << "\nBoundary [" << i << "]\n" << "  Name: " << name << ",  Type: " << type << "\n";

        // All types have an "init" object
        const map<string,cJsonValue> & initObj = bndObj.at( "init" ).AsObject();

        cout << std::fixed << setprecision( 6 );

        // --- freestream ---
        if ( type == "freestream" )
        {
            // profile_file?
            if ( initObj.count( "profile_file" ) )
            {
                cout << "  Profile file: " << initObj.at( "profile_file" ).AsString() << "\n";
                prof_nm = initObj.at( "profile_file" ).AsString();
                q263bcs(ig, prof_nm);

            }
            // inline profile_data?
            else if ( initObj.count( "profile_data" ) )
            {
                const map<string,cJsonValue> & pd = initObj.at( "profile_data" ).AsObject();

                const vector<cJsonValue> & rArr = pd.at( "radius" ).AsArray();
                size_t pts = rArr.size();

                vector<Real> r( pts ), vx( pts ), vy( pts ), vz( pts ), p( pts ), T( pts ), kk( pts ), om( pts );
                for ( size_t j = 0; j < pts; ++j )
                {
                    r[j] = rArr[j].AsNumber();
                }
                _Extract( pd, "velocity_x",  vx );
                _Extract( pd, "velocity_y",  vy );
                _Extract( pd, "velocity_z",  vz );
                _Extract( pd, "pressure",     p );
                _Extract( pd, "temperature",  T );
                _Extract( pd, "k",            kk );
                _Extract( pd, "omega",        om );

                std::cout << "  Inline profile_data (" << pts << " pts):\n"
                          << "    " << std::setw( 8 ) << "r"
                          << std::setw(12) << "vx"
                          << std::setw(12) << "vy"
                          << std::setw(12) << "vz"
                          << std::setw(12) << "p"
                          << std::setw(12) << "T"
                          << std::setw(12) << "k"
                          << std::setw(12) << "om\n";

                for ( size_t j = 0; j < pts; ++j )
                {
                    std::cout << "    "
                              << std::setw( 8 ) << r[j]
                              << std::setw(12) << vx[j]
                              << std::setw(12) << vy[j]
                              << std::setw(12) << vz[j]
                              << std::setw(12) << p[j]
                              << std::setw(12) << T[j]
                              << std::setw(12) << kk[j]
                              << std::setw(12) << om[j]
                              << "\n";
                }
            }
            // uniform fallback
            else if ( initObj.count( "uniform" ) )
            {
                const map<string,cJsonValue> & u = initObj.at( "uniform" ).AsObject();

                Real k, om;
                Real vx = u.at( "velocity_x"  ).AsNumber();
                Real vy = u.at( "velocity_y"  ).AsNumber();
                Real vz = u.at( "velocity_z"  ).AsNumber();
                Real p  = u.at( "pressure"    ).AsNumber();
                Real T  = u.at( "temperature" ).AsNumber();

                for(iv=0; iv<nv; iv++) tmpq[iv] = -1;
                if(fld->gettype()==mfroe_gas_cht)
                {
                    Int medium = u.at("medium").AsNumber();
                    tmpq[0] = vx;
                    tmpq[1] = vy;
                    tmpq[2] = vz;
                    tmpq[3] = T;
                    tmpq[4] = p;
                    tmpq[5] = medium;
                    cout << "  Uniform:\n"
                              << "    velocity_x:   " << vx << "\n"
                              << "    velocity_y:   " << vy << "\n"
                              << "    velocity_z:   " << vz << "\n"
                              << "    pressure:     " << p  << "\n"
                              << "    temperature:  " << T  << "\n"
                              << "    medium:       " << medium  << "\n";
                }
                else
                {
                    tmpq[0] = vx;
                    tmpq[1] = vy;
                    tmpq[2] = vz;
                    tmpq[3] = T;
                    tmpq[4] = p;
                    cout << "  Uniform:\n"
                              << "    velocity_x:   " << vx << "\n"
                              << "    velocity_y:   " << vy << "\n"
                              << "    velocity_z:   " << vz << "\n"
                              << "    pressure:     " << p  << "\n"
                              << "    temperature:  " << T  << "\n";
                }

                if(vsc->gettype()==komegalowre_visc)
                {
                    k  = u.at( "k"           ).AsNumber();
                    om = u.at( "omega"       ).AsNumber();
                    tmpq[nv0] = k;
                    tmpq[nv0+1] = omega;
                    cout << "    k:            " << k  << "\n"
                         << "    omega:        " << om << "\n";
                }

                if(fld->gettype()==mfreacting_gas)
                {
                    Real z = u.at( "mixture_fraction" ).AsNumber();
                    cout << setw( 20 ) << "mixture_fraction:" << setw(14) << z << "\n";
                    tmpq[nv0-1] = z;
                }

                unibcs(ig,tmpq);
            }
        }
        // --- sub_in (uniform or profile_data) ---
        else if ( type == "sub_in" )
        {
            if ( initObj.count( "profile_data" ) )
            {
                const map<string,cJsonValue> & pd = initObj.at( "profile_data" ).AsObject();

                const vector<cJsonValue> & rArr = pd.at( "radius" ).AsArray();
                size_t pts = rArr.size();

                vector<Real> r( pts ), tp( pts ),tT( pts ), fa1( pts ), fa2( pts );
                for ( size_t j = 0; j < pts; ++j )
                {
                    r[j] = rArr[j].AsNumber();
                }
                _Extract( pd, "total_pressure",    tp );
                _Extract( pd, "total_temperature", tT );
                _Extract( pd, "whirl_angle",      fa1 );
                _Extract( pd, "pitch_angle",      fa2 );

                cout << "  sub_in profile (" << pts << " pts):\n"
                          << "    " << std::setw( 8 ) << "r"
                          << std::setw(14) << "Pt"
                          << std::setw(14) << "Tt"
                          << std::setw(14) << "a1"
                          << std::setw(14) << "a2\n";

                for ( size_t j = 0; j < pts; ++j )
                {
                    std::cout << "    "
                              << std::setw( 8 ) << r[j]
                              << std::setw(14) << tp[j]
                              << std::setw(14) << tT[j]
                              << std::setw(14) << fa1[j]
                              << std::setw(14) << fa2[j]
                              << "\n";
                }
                assert(0);
            }
            else if ( initObj.count( "uniform" ) )
            {
                const map<string,cJsonValue> & u = initObj.at( "uniform" ).AsObject();

                Real tp, tT, whirl_angle, pitch_angle, k, omega, z;

                tp  = u.at( "total_pressure"   ).AsNumber();
                tT  = u.at( "total_temperature").AsNumber();
                whirl_angle = u.at( "whirl_angle"     ).AsNumber();
                pitch_angle = u.at( "pitch_angle"     ).AsNumber();

                for(iv=0; iv<nv; iv++) tmpq[iv] = -1;
                tmpq[0] = whirl_angle;
                tmpq[1] = pitch_angle;
                tmpq[3] = tT;
                tmpq[4] = tp;
                cout << "  sub_in uniform:\n"
                          << "    total_pressure:    " << tp  << "\n"
                          << "    total_temperature: " << tT  << "\n"
                          << "    whirl_angle:       " << whirl_angle << "\n"
                          << "    pitch_angle:       " << pitch_angle << "\n";
                if(fld->gettype()==mfroe_gas_cht)
                {
                    Real medium = u.at( "medium"     ).AsNumber();
                    tmpq[nv0-1] = medium;
                    cout << "    medium:       " << medium << "\n";
                }

                if(vsc->gettype()==komegalowre_visc)
                {
                    tmpq[nv0] = k;
                    tmpq[nv0+1] = omega;

                    if(u.count( "k"))   k = u.at( "k"     ).AsNumber();
                    else                k = 4;
    
                    if(u.count( "omega"))   omega = u.at( "omega" ).AsNumber();
                    else                    omega = 44444;

                    cout << "    k:                 " << k << "\n"
                         << "    omega:             " << omega << "\n";
                }

                if(fld->gettype()==mfreacting_gas)
                {
                    z = u.at( "mixture_fraction" ).AsNumber();
                    cout << setw( 20 ) << "mixture_fraction:" << setw(14) << z << "\n";
                    tmpq[nv0-1] = z;
                }

                subinbcs(ig, tmpq);
            }
        }
        // --- sub_out (uniform or profile_data) ---
        else if ( type == "sub_out" )
        {
            if ( initObj.count( "profile_data" ) )
            {
                const map<string,cJsonValue> & pd = initObj.at( "profile_data" ).AsObject();

                const vector<cJsonValue> & rArr = pd.at( "radius" ).AsArray();
                size_t pts = rArr.size();

                vector<Real> r( pts ), sp( pts );
                for ( size_t j = 0; j < pts; ++j )
                {
                    r[j] = rArr[j].AsNumber();
                }
                _Extract( pd, "static_pressure", sp );

                cout << "  sub_out profile (" << pts << " pts):\n"
                          << "    " << std::setw( 8 ) << "r"
                          << std::setw(14) << "Ps\n";

                for ( size_t j = 0; j < pts; ++j )
                {
                    cout << "    "
                              << std::setw( 8 ) << r[j]
                              << std::setw(14) << sp[j]
                              << "\n";
                }
            }
            else if ( initObj.count( "uniform" ) )
            {
                const map<string,cJsonValue> & u = initObj.at( "uniform" ).AsObject();

                Real sp = u.at( "static_pressure" ).AsNumber();

                cout << "  static_pressure: " << sp << "\n";

                for(iv=0; iv<nv; iv++) tmpq[iv] = -1;
                tmpq[4] = sp;

                if(fld->gettype()==mfroe_gas_cht)
                {
                    Real medium = u.at( "medium"     ).AsNumber();
                    tmpq[nv0-1] = medium;

                    cout << "    medium:       " << medium << "\n";
                }
                if(fld->gettype()==mfreacting_gas)
                {
                    Real z = u.at( "mixture_fraction" ).AsNumber();
                    cout << setw( 20 ) << "mixture_fraction:" << setw(14) << z << "\n";
                    tmpq[nv0-1] = z;
                }

                suboutbcs(ig, tmpq);
            }
        }
        // --- inviscid_wall & viscous_wall ---
        else if ( type == "inviscid_wall" )
        {
            const map<string,cJsonValue> & var = initObj.at( "uniform" ).AsObject();

            if ( var.count( "rotational_speed" ) )
            {
                Real rs = var.at( "rotational_speed" ).AsNumber();
                omegb[ig] = rs;
                cout << "  rotational_speed: " << rs << "\n";
            }
            if ( var.count( "wall_temperature" ) )
            {
                Real wt = var.at( "wall_temperature" ).AsNumber();
                cout << "  wall_temperature: " << wt << "\n";
            }
            if(!b_restart) adiabcs(ig);
            else {cout << "  restart " << bgnm[ig] << "\n"; }
        }
        else if ( type == "viscous_wall" )
        {
            const map<string,cJsonValue> & var = initObj.at( "uniform" ).AsObject();

            if ( var.count( "rotational_speed" ) )
            {
                Real rs = var.at( "rotational_speed" ).AsNumber();
                omegb[ig] = rs;
                cout << "  rotational_speed: " << rs << "\n";
            }
            if ( var.count( "wall_temperature" ) )
            {
                Real wt = var.at( "wall_temperature" ).AsNumber();
                cout << "  wall_temperature: " << wt << "\n";
            }
            if(!b_restart) adiabcs(ig);
            else {cout << "  restart " << bgnm[ig] << "\n"; }
        }
        // --- sliding_plane ---
        else if ( type == "sliding_plane" )
        {
            // profile_file?
            if ( initObj.count( "profile_file" ) )
            {
                cout << "  Profile file: " << initObj.at( "profile_file" ).AsString() << "\n";
                prof_nm = initObj.at( "profile_file" ).AsString();
                if(!b_restart) q263bcs(ig, prof_nm);
                else {cout << "  restart " << bgnm[ig] << "\n"; }
            }
            // uniform?
            else if ( initObj.count( "uniform" ) )
            {
                const map<string,cJsonValue> & u = initObj.at( "uniform" ).AsObject();

                Real vx = u.at( "velocity_x"  ).AsNumber();
                Real vy = u.at( "velocity_y"  ).AsNumber();
                Real vz = u.at( "velocity_z"  ).AsNumber();
                Real p  = u.at( "pressure"    ).AsNumber();
                Real T  = u.at( "temperature" ).AsNumber();
                Real k  = u.at( "k"           ).AsNumber();
                Real om = u.at( "omega"       ).AsNumber();

                cout << "  sliding_plane uniform:\n"
                          << "    vx: " << vx
                          << ", vy: " << vy
                          << ", vz: " << vz << "\n"
                          << "    p:  " << p
                          << ", T:  " << T << "\n"
                          << "    k:  " << k
                          << ", omega: " << om << "\n";
                if(!b_restart) unibcs(ig,tmpq);
                else {cout << "  restart " << bgnm[ig] << "\n"; }
            }
            // profile_data?
            else if ( initObj.count( "profile_data" ) )
            {
                const map<string,cJsonValue> & pd = initObj.at( "profile_data" ).AsObject();

                const vector<cJsonValue> & rArr = pd.at( "radius" ).AsArray();
                size_t pts = rArr.size();

                vector<Real> r( pts ),vx( pts ), vy( pts ),vz( pts ),p( pts ),T( pts ),kk( pts ),om( pts );
                for ( size_t j = 0; j < pts; ++j )
                {
                    r[j] = rArr[j].AsNumber();
                }
                _Extract( pd, "velocity_x",  vx );
                _Extract( pd, "velocity_y",  vy );
                _Extract( pd, "velocity_z",  vz );
                _Extract( pd, "pressure",     p );
                _Extract( pd, "temperature",  T );
                _Extract( pd, "k",            kk );
                _Extract( pd, "omega",        om );

                cout << "  sliding_plane profile_data ("<<pts<<" pts):\n"
                          << "    " << std::setw(8) << "r"
                          << std::setw(12) << "vx"
                          << std::setw(12) << "vy"
                          << std::setw(12) << "vz"
                          << std::setw(12) << "p"
                          << std::setw(12) << "T"
                          << std::setw(12) << "k"
                          << std::setw(12) << "om\n";

                for ( size_t j = 0; j < pts; ++j )
                {
                    std::cout << "    "
                              << std::setw( 8 ) << r[j]
                              << std::setw(12) << vx[j]
                              << std::setw(12) << vy[j]
                              << std::setw(12) << vz[j]
                              << std::setw(12) << p[j]
                              << std::setw(12) << T[j]
                              << std::setw(12) << kk[j]
                              << std::setw(12) << om[j]
                              << "\n";
                }
            }
        }
        else if ( type == "thermal_wall" )
        {
            if ( initObj.count( "uniform" ) )
            {
                const map<string,cJsonValue> & u = initObj.at( "uniform" ).AsObject();

                Real T  = u.at( "temperature" ).AsNumber();

                for(iv=0; iv<nv; iv++) tmpq[iv] = -1;
                if(fld->gettype()==mfroe_gas_cht)
                {
                    tmpq[0] = 0.;
                    tmpq[1] = 0.;
                    tmpq[2] = 0.;
                    tmpq[3] = T;
                    tmpq[4] = 0.;
                    tmpq[5] = 0.;
                    tmpq[6] = 0.;
                    tmpq[7] = 0.;
                }
                else
                {
                    tmpq[0] = 0.;
                    tmpq[1] = 0.;
                    tmpq[2] = 0.;
                    tmpq[3] = T;
                    tmpq[4] = 0.;
                    tmpq[5] = 0.;
                    tmpq[6] = 0.;
                }

                cout << "  Uniform:\n"
                          << "    temperature:  " << T  << "\n";

                unibcs(ig,tmpq);
            }

        }
        // --- periodic ---
        else if ( type == "periodic" )
        {
            cout << "  (periodic — no init)\n";
        }
        else if ( type == "massflow" )
        {
            if ( initObj.count( "uniform" ) )
            {
                const map<string,cJsonValue> & u = initObj.at( "uniform" ).AsObject();

                Real T, umag, k, omega, z;

                T     = u.at( "T"   ).AsNumber();
                umag  = u.at( "velocity_mag").AsNumber();

                for(iv=0; iv<nv; iv++) tmpq[iv] = -1;
                tmpq[0] = umag;
                tmpq[1] = 0;
                tmpq[2] = 0;
                tmpq[3] = T;
                tmpq[4] = 0;
                cout << "  massflow uniform:\n"
                          << "    T:            " << T  << "\n"
                          << "    velocity-mag: " << umag  << "\n";

                if(fld->gettype()==mfroe_gas_cht)
                {
                    Real medium = u.at( "medium"     ).AsNumber();
                    tmpq[nv0-1] = medium;
                    cout << "    medium:       " << medium << "\n";
                }

                if(vsc->gettype()==komegalowre_visc)
                {
                    tmpq[nv0] = k;
                    tmpq[nv0+1] = omega;

                    if(u.count( "k"))   k = u.at( "k"     ).AsNumber();
                    else                k = 4;
    
                    if(u.count( "omega"))   omega = u.at( "omega" ).AsNumber();
                    else                    omega = 44444;

                    cout << "    k:                 " << k << "\n"
                         << "    omega:             " << omega << "\n";
                }

                if(fld->gettype()==mfreacting_gas)
                {
                    z = u.at( "mixture_fraction" ).AsNumber();
                    cout << setw( 20 ) << "mixture_fraction:" << setw(14) << z << "\n";
                    tmpq[nv0-1] = z;
                }

                unibcs(ig, tmpq);
            }
        }
    }

    for( ig=0;ig<ng;ig++ ) 
    {  
        cout << "rotational speed of boundary " << bgnm[ig] << " is " <<    omegb[ig] << "\n";
    }
}
