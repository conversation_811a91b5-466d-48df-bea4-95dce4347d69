   using namespace std;

#  include <sstream>
#  include <domain/cfd/domain.h>
#  include <domain/cfd/init_sol/q263.h>

   bool readsolutionbin( string fnm, Int *nx, Int *nv, Int *nq, Int *naux,
                         Real *xq[3], Real *q[20], Real *aux[20], Int *ng, 
                         string *bgnm,Int *nbb,Real *xb[20][3],Real *qb[20][20],
                         Real *auxb[20][20]);

   void cFdDomain::init_vol()
  {
      bool bsu;

      bsu = false;

//      if(sido=="uniinit")   
//     {
//         uni_init(&bsu);
//     }
//      else if(sido=="q263init") 
//     {
//         q263_init(&bsu);
//     }
//      else if(sido=="restartvolinit") 
//     {
//         restart_vol_init(&bsu);
//     }
//      else 
//     {
//         cout << "Error: unkown way to initialize the solution, try to uniinit\n";
//         uni_init(&bsu);
//     }
  }

   void cFdDomain::uni_init(Real *q0)
  {
      Int iv,iq;

     #pragma acc enter data copyin(q0[0:nv])

     #pragma acc parallel loop gang vector\
      present (sq[0:nv*nq],q0[0:nv],this) \
      default(none)
      for( iq=0;iq<nq;iq++ )
     {
         for( iv=0;iv<nv;iv++ )
        {
            //q(iv,iq)= q0[iv];
            sq[ADDR(iv,iq,nq)]= q0[iv];
        }
     }
     #pragma acc exit data copyout(q0[0:nv])

      //coo->zvel( 0,nq, NULL, xq, q,q );
      coo->zvel( 0,nq, xq, q,q );
//      delete[] q0;

/*    for( iq=0;iq<nq;iq++ )
     {
         for( iv=0;iv<nv;iv++ )
        {
            cout << q[iv][iq]<<" ";
        }
         cout << "\n";
     }*/
  }

   void cFdDomain::q263_init(string prof_nm)
  {
      cQ263 *qinit;
      string fnm, cpath;
      int tmpidone[100];

      fnm= dev->getcpath();
      fnm= fnm+"/"+prof_nm;

      cout <<"The profile file to initilize the solution is " << fnm << "\n";
      qinit = new cQ263();
      qinit->setnv(nv);
      qinit->read(fnm);
      qinit->buildgrid();
      qinit->interp(nx, nv, nq, xq, q, tmpidone);

      delete qinit; qinit=NULL;
  }

   void cFdDomain::restart_vol_init()
  {
      cPdata      *dofl,*ptsl;
      pickle_t     buf;
      size_t       len,l;
      string       fnme;
      Int          icpu;
      Int          i,j, il, iv, iq;
      FILE        *f;
      Real        *sxpl=NULL,*sxql=NULL,*sql=NULL,*sauxl=NULL, *sul[NTLEV];
      cAu3xView<Real> ql, ul[NTLEV];
      Real rdum;
      Int          tmpntlv;
      Real         tmpcfl;


      for(il=0; il<NTLEV; il++)
     {
         sul[il]=NULL;
     }

      fnme= dev->getcpath();
      icpu= dev->getrank();
      fnme= fnme+"/"+dev->getname()+".restart.q."+strc(ilev)+"."+strc(icpu);
      //fnme= "./restart/"+dev->getname()+".restart.q."+strc(ilev)+"."+strc(icpu);
      f= fopen(fnme.c_str(),"r");
      cout << "open restart file " << fnme << "\n";

      l= fread( &tm,  1,sizeof(rdum),f );
      l= fread( &len,1,sizeof(len),f );
      buf= new pickle_v[len];
      cout << "len " << len << "\n";
      l= fread(  buf,1,        len,f );

      len=0;
      dofl= new cPdata( dev );
      ptsl= new cPdata( dev );

      dofl->unpickle( &len,buf );
      ptsl->unpickle( &len,buf );
      delete[] buf;buf= NULL; len=0;

      ptsl->read( nx,   &sxpl,f );
      dofl->read( nx,   &sxql,f );
      dofl->read( nv,    &sql,f );
      dofl->read( naux,&sauxl,f );
      l= fread( &tmpntlv,  1,sizeof(tmpntlv),f );
      for(il=0; il<tmpntlv; il++)
     {
         dofl->read( nv, &(sul[il]),f );
     }
      l= fread( &tmpcfl,  1,sizeof(rdum),f );
      fclose(f);

      cfl = tmpcfl;

      cout << "restart from physical time " << tm << " with cfl " << cfl << "\n";

     #pragma acc enter data copyin(sql[0:nv*nq])
      for(il=0; il<tmpntlv; il++)
     {
         #pragma acc enter data copyin(sul[il][0:nv*nq])
     }

      ql.subv( nv,nq, sql );
      fld->redim( 0,nq, ql );

     #pragma acc parallel loop gang vector\
      present (sq[0:nv*nq],sql[0:nv*nq],this) \
      default(none)
      for( i=0;i<nq;i++ )
     {
         for( j=0;j<nv;j++ )
        {
            //q[j][i]= ql[j][i];
            sq[ADDR(j,i,nq)]= sql[ADDR(j,i,nq)];
        }
     }

      //DO NOT NORMALIZE U HERE!
      for(il=0; il<tmpntlv; il++)
     {
        #pragma acc parallel loop gang vector\
         present (su[il][0:nv*nq],sul[il][0:nv*nq],this) \
         default(none)
         for( i=0;i<nq;i++ )
        {
            for( j=0;j<nv;j++ )
           {
               //u[j][i]= ul[j][i];
               su[il][ADDR(j,i,nq)]= sul[il][ADDR(j,i,nq)];
           }
        }
     }

     /* for(il=0; il<tmpntlv; il++)
     {
        for( i=0;i<nq;i++ )
       {
          for( j=0;j<nv;j++ )
         {
           cout << u[j][i] << " ";
         }
          cout << "\n";
       }
     }*/

     #pragma acc exit data delete(sql[0:nv*nq])
      for(il=0; il<tmpntlv; il++)
     {
         #pragma acc exit data delete(sul[il][0:nv*nq])
     }
      ptsl->destroy(   &sxpl );
      dofl->destroy(   &sxql );
      dofl->destroy(    &sql );
      dofl->destroy(  &sauxl );
      for(il=0; il<tmpntlv; il++)
     {
         dofl->destroy(  &(sul[il]) );
     }
      delete ptsl; ptsl=NULL;
      delete dofl; dofl=NULL;

      for(Int ig=0; ig<ng; ig++)
     {
         restartbcs(ig);    
     }
//exit(0);
  }

   //extern "C" void spsginit( cDomain *dom, void *rftr, Int nx, Int nv, Int nq, Real *xq[], Real *q[], Real *tm,
   //                          Real *su[], bool *bsu, Int naux, Real *cfl, Int *idone )
   void cFdDomain::spsg_init( string spsg_sol, Int npsg )
  {
      Int iv,iq, ix, ib, iq0, jq;
//      cCosystem *coo;
//      cDevice *dev;
      string cpath, devnm, fnm;
      bool btmp;
      Int tmpnx, tmpnv, tmpnq, tmpng, tmpnaux, tmpnbb[20], *infected[1], asct, gsct;
      Real *tmpxq[3], *tmpq[20], *tmpaux[20],*tmpxb[20][3], *tmpqb[20][20], *tmpauxb[20][20];
      string tmpbgnm[20];
      Real *tmpxrt[3], ptch;
      ifstream fle;
      Real y[3], y0[3], y1[3], dl[3], d;
//      cField      *fld;
   
      cout << "initialize multi-passage with a single passage solution\n";

      tm = 0;
 
//      dev= dom->device();
      devnm = dev->getname();
      cpath= dev->getcpath();
      fnm= cpath+ "/" + spsg_sol;

//      coo= dom->cosystem();

      //read single passage solutions
      btmp = readsolutionbin( fnm, &tmpnx, &tmpnv, &tmpnq, &tmpnaux, tmpxq, tmpq, tmpaux, &tmpng, 
                              tmpbgnm, tmpnbb, tmpxb, tmpqb, tmpauxb );
      if(!btmp) 
     {
         cout << "Error: can not read the single passage solution\n";
         return;      
     }

      cTabData data;
      coo->get( &data );
      data.get( "assembly-sectors", &asct );

//      fld= dom->field();

//      fnm= "./nb/" + devnm + ".dat";
//      fle.open(fnm.c_str());
//      fle >> npsg;
//      fle.close();
//      cout << "number of passage is " << npsg << "\n";
      ptch = pi2/((Real)asct*(Real)npsg); //pitch of the original blade assembly

      //cout << "get here0\n";
      //convert coordinates into cylindrical coordinates
      tmpxrt[0] = new Real [tmpnq]; 
      tmpxrt[1] = new Real [tmpnq]; 
      tmpxrt[2] = new Real [tmpnq]; 
     
      infected[0] = new Int [tmpnq];
      for(iq=0; iq<tmpnq; iq++) infected[0][iq] = iq;
      coo->bcoor( 0,tmpnq, infected, tmpxq, tmpxrt );
      coo->bvel(  0,tmpnq, infected, tmpxq, tmpq, tmpq );
      delete[] infected[0]; infected[0]=NULL;
      for(iq=0; iq<tmpnq; iq++)
     {
        if(tmpxrt[2][iq]<0.) tmpxrt[2][iq]+=pi2;
     }

     /* ofstream ofle;
      ofle.open("single_qx.dat");
      for(iq=0; iq<tmpnq; iq++)
     {
        //ofle << tmpxrt[0][iq] << " " << tmpxrt[1][iq] << " " << tmpxrt[2][iq] << "\n";
        ofle << tmpxq[0][iq] << " " << tmpxq[1][iq] << " " << tmpxq[2][iq] << "\n";
     }
      ofle.close();*/

      //cout << "get here1 " << tmpnq << " " << nq << "\n";
      if(nq % tmpnq != 0)
     {
         cout << "Error: the number of elements is not a mulitple of the single passage solution, abort\n";
         assert(0);
     }

      Int ntmp=0;
      for(iq=0; iq<nq; iq++)
     {
         Real dt, t;

         y[0] = sxq[ADDR(0,iq,nq)];
         y[1] = sxq[ADDR(1,iq,nq)];
         y[2] = sxq[ADDR(2,iq,nq)];
         y0[0] = y[0];                         //x
         y0[1] = sqrt(y[1]*y[1] + y[2]*y[2]);  //r
         y0[2] = atan2(y[1], y[2]);            //theta
         if(y0[2]<0.) y0[2]+=pi2;

         for(jq=0; jq<tmpnq; jq++)
        {
            y1[0] = tmpxrt[0][jq];    
            y1[1] = tmpxrt[1][jq];    
            y1[2] = tmpxrt[2][jq];    
      
            //if two points match, they must have the same x-r coordinates the difference of the theta coordinates 
            //must be a multiple of pitch
  
            bool btmp;
            btmp = false; 
            if(fabs(y0[0]-y1[0])<1e-9) 
           {
               //same x
               if(fabs(y0[1]-y1[1])<1e-9) 
              {
                 //same r
                 for(ib=0; ib<npsg; ib++)
                {
                    t = y1[2] + ib*ptch;
                    if(t>pi2) t = t - pi2;
                    dt = fabs(y0[2] - t);
                    //cout << dt << "\n";
                    if(dt<1e-9)
                   {
                       //the difference in theta is a multiple of pitch then this two points match
                       btmp = true;
                       break;
                   }
                }
              }
           }

            if(btmp)
           {
               for(iv=0; iv<nv; iv++)
              {
                  sq[ADDR(iv,iq,nq)] = tmpq[iv][jq];
              }
               //cout << q[0][iq] << " " << q[1][iq] << " " << q[2][iq] << " " << q[3][iq]  << " "
               //     << q[4][iq] << " " << q[5][iq] << "\n";
               break;  
           }
        }
         if(jq==tmpnq)
        {
            ntmp++;
        }
     }
      //cout << "get here2 \n";
      fld->redim( 0,nq, q );
      coo->zvel( 0,nq, NULL, xq, q,q );
      cout << ntmp << " elements are left to initialize in the single passage initializer for device "<<devnm << "\n";

      if(ntmp>0)
     {
         cout << "Error: for device " << devnm << " " << ntmp << " elements are not initialized, check your configuraiton, abort\n";
         assert(0);
     }

      //use all variables of the single passage solution
      //for(iv=0; iv<nv; iv++) idone[iv] = 1;

      for(Int ig=0; ig<tmpng; ig++)
     {
         for(ix=0; ix<tmpnx; ix++)
        {
            delete[] tmpxb[ig][ix]; tmpxb[ig][ix]=NULL;
        }
         for(iv=0; iv<tmpnv; iv++)
        {
            delete[] tmpqb[ig][iv]; tmpqb[ig][iv]=NULL;
        }
         for(iv=0; iv<2; iv++)
        {
            delete[] tmpauxb[ig][iv]; tmpauxb[ig][iv]=NULL;
        }
     }
      for(ix=0; ix<tmpnx; ix++)   delete[] tmpxq[ix];  tmpxq[ix]=NULL;
      for(iv=0; iv<tmpnv; iv++)   delete[] tmpq[iv];   tmpq[iv]=NULL;
      for(iv=0; iv<tmpnaux; iv++) delete[] tmpaux[iv]; tmpaux[iv]=NULL;

      delete[] tmpxrt[0]; tmpxrt[0]=NULL;
      delete[] tmpxrt[1]; tmpxrt[1]=NULL;
      delete[] tmpxrt[2]; tmpxrt[2]=NULL;
  }

   bool readsolutionbin( string fnm, Int *nx, Int *nv, Int *nq, Int *naux,
                         Real *xq[3], Real *q[20], Real *aux[20], Int *ng, 
                         string *bgnm,Int *nbb,Real *xb[20][3],Real *qb[20][20],
                         Real *auxb[20][20])
  {
      FILE *f;
      Int idum, ib, ix, iv, ig, iq;
      Real rdum;
      string sdum;
      Real *sxq,*sq,*saux; 
      Real *tmpxq[3], *tmpq[20], *tmpaux[20];
      Real *sxb[20], *sqb[20], *sauxb[20];
      Real *tmpxb[20][3], *tmpqb[20][20], *tmpauxb[20][20];
      size_t i;

      f= fopen(fnm.c_str(),"r");
      i = fread( nx,1,  sizeof(idum),f );
      i = fread( nv,1,  sizeof(idum),f );
      i = fread( nq,1,  sizeof(idum),f );
      i = fread( naux,1,sizeof(idum),f );

      sxq = new Real [(*nx)*(*nq)];
      i = fread( sxq, (*nx)*(*nq),   sizeof(rdum),f );

      sq = new Real [(*nv)*(*nq)];
      i = fread( sq,  (*nv)*(*nq),   sizeof(rdum),f );

      saux = new Real [(*naux)*(*nq)];
      i = fread( saux, (*naux)*(*nq), sizeof(rdum),f );

      i = fread( ng  ,   1, sizeof(idum),f );
     //i = fread( bgnm, *ng, sizeof(sdum), f);
      i = fread( nbb,  *ng, sizeof(idum), f);
      for(ig=0; ig<*ng; ig++)
     {
         sxb[ig] = new Real [(*nx)*nbb[ig]];        
         i = fread( sxb[ig],   (*nx)*nbb[ig], sizeof(rdum), f);

         sqb[ig] = new Real [(*nv)*nbb[ig]];        
         i = fread( sqb[ig],   (*nv)*nbb[ig], sizeof(rdum), f);

         sauxb[ig] = new Real [2*nbb[ig]];        
         i = fread( sauxb[ig],  2*nbb[ig], sizeof(rdum), f);
     }
      fclose(f);

      subv(   *nx,*nq,   sxq, tmpxq );
      subv(   *nv,*nq,   sq,  tmpq );
      subv( *naux,*nq, saux,  tmpaux );
      for(ig=0; ig<*ng; ig++)
     {
        subv( *nx,  nbb[ig], sxb[ig],    tmpxb[ig] );
        subv( *nv,  nbb[ig], sqb[ig],    tmpqb[ig] );
        subv( 2,    nbb[ig], sauxb[ig],  tmpauxb[ig] );
     }

    

      cout << "nx " << *nx << "\n";
      cout << "nv " << *nv << "\n";
      cout << "nq " << *nq << "\n";
      cout << "naux " << *naux << "\n";
      for(ig=0; ig<*ng; ig++)
     {
         cout << ig << " " << bgnm[ig] << " " << nbb[ig] << "\n";
     }

      for(ix=0; ix<*nx; ix++)  
     {
        xq[ix] = new Real [*nq];
        for(iq=0; iq<*nq; iq++)
       {
          xq[ix][iq] = tmpxq[ix][iq];
       }
     }
      for(iv=0; iv<*nv; iv++)  
     {
        q[iv] = new Real [*nq];
        for(iq=0; iq<*nq; iq++)
       {
          q[iv][iq] = tmpq[iv][iq];
       }
     }
      for(iv=0; iv<*naux; iv++) 
     {
        aux[iv]  = new Real [*nq];
        for(iq=0; iq<*nq; iq++)
       {
          aux[iv][iq] = tmpaux[iv][iq];
       }
     }


      for(ig=0; ig<*ng; ig++)
     {
        for(ix=0; ix<*nx; ix++)
       {
          xb[ig][ix] = new Real [nbb[ig]];
          for(ib=0; ib<nbb[ig]; ib++)
         {
            xb[ig][ix][ib] = tmpxb[ig][ix][ib];
         }
       }
        for(iv=0; iv<*nv; iv++)
       {
          qb[ig][iv] = new Real [nbb[ig]];
          for(ib=0; ib<nbb[ig]; ib++)
         {
            qb[ig][iv][ib] = tmpqb[ig][iv][ib];
         }
       }
        for(iv=0; iv<2; iv++)
       {
          auxb[ig][iv] = new Real [nbb[ig]];
          for(ib=0; ib<nbb[ig]; ib++)
         {
            auxb[ig][iv][ib] = tmpauxb[ig][iv][ib];
         }
       }
     }

      delete[] sxq; sxq=NULL;
      delete[] sq; sq=NULL;
      delete[] saux; saux=NULL;
      for(ig=0; ig<*ng; ig++)
     {
         delete[] sxb[ig]; sxb[ig]=NULL;
         delete[] sqb[ig]; sqb[ig]=NULL;
         delete[] sauxb[ig]; sauxb[ig]=NULL;
     }

      return true;
  }
